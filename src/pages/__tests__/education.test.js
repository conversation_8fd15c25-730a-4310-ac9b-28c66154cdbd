import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import EducationPage from '../education';

// Mock the Docusaurus components
jest.mock('@theme/Layout', () => {
  return function MockLayout({ children }) {
    return <div data-testid="layout">{children}</div>;
  };
});

jest.mock('@docusaurus/Translate', () => {
  return {
    __esModule: true,
    default: function MockTranslate({ children, id }) {
      return <span data-testid={`translate-${id}`}>{children}</span>;
    },
    translate: ({ message }) => message,
  };
});

jest.mock('@theme/Heading', () => {
  return function MockHeading({ children, as: Component = 'h1' }) {
    return <Component>{children}</Component>;
  };
});

jest.mock('@docusaurus/Link', () => {
  return function MockLink({ children, to, className }) {
    return (
      <a href={to} className={className}>
        {children}
      </a>
    );
  };
});

jest.mock('../../components/EducationStructuredData', () => {
  return function MockEducationStructuredData() {
    return <div data-testid="structured-data" />;
  };
});

jest.mock('../../components/EducationFAQ', () => {
  return function MockEducationFAQ() {
    return <div data-testid="education-faq" />;
  };
});

describe('EducationPage', () => {
  beforeEach(() => {
    render(<EducationPage />);
  });

  test('renders the main layout', () => {
    expect(screen.getByTestId('layout')).toBeInTheDocument();
  });

  test('includes structured data for SEO', () => {
    expect(screen.getByTestId('structured-data')).toBeInTheDocument();
  });

  test('includes FAQ section', () => {
    expect(screen.getByTestId('education-faq')).toBeInTheDocument();
  });

  test('renders hero section with main title', () => {
    expect(screen.getByTestId('translate-education.hero.title')).toBeInTheDocument();
  });

  test('renders challenges section', () => {
    expect(screen.getByTestId('translate-education.challenges.title')).toBeInTheDocument();
  });

  test('renders solution section', () => {
    expect(screen.getByTestId('translate-education.solution.title')).toBeInTheDocument();
  });

  test('renders features section', () => {
    expect(screen.getByTestId('translate-education.features.title')).toBeInTheDocument();
  });

  test('renders benefits section', () => {
    expect(screen.getByTestId('translate-education.benefits.title')).toBeInTheDocument();
  });

  test('renders case studies section', () => {
    expect(screen.getByTestId('translate-education.cases.title')).toBeInTheDocument();
  });

  test('renders integration section', () => {
    expect(screen.getByTestId('translate-education.integration.title')).toBeInTheDocument();
  });

  test('renders future section', () => {
    expect(screen.getByTestId('translate-education.future.title')).toBeInTheDocument();
  });

  test('renders CTA section', () => {
    expect(screen.getByTestId('translate-education.cta.title')).toBeInTheDocument();
  });

  test('contains links to FunBlocks products', () => {
    const links = screen.getAllByRole('link');
    const funblocksLinks = links.filter(link => 
      link.getAttribute('href')?.includes('funblocks.net')
    );
    expect(funblocksLinks.length).toBeGreaterThan(0);
  });
});
