import React from 'react';
import Layout from '@theme/Layout';
import Translate, { translate } from '@docusaurus/Translate';
import Heading from '@theme/Heading';
import Link from '@docusaurus/Link';
import clsx from 'clsx';
import styles from './education.module.css';
import EducationStructuredData from '../components/EducationStructuredData';
import EducationFAQ from '../components/EducationFAQ';

// Hero Section Component
function HeroSection() {
  return (
    <section className={styles.heroSection}>
      <div className="container">
        <div className={styles.heroContent}>
          <Heading as="h1" className={styles.heroTitle}>
            <Translate id="education.hero.title">
              FunBlocks AI Empowers Education: Ushering in a New Era of Intelligent Learning
            </Translate>
          </Heading>
          <p className={styles.heroSubtitle}>
            <Translate id="education.hero.subtitle">
              All-in-One AI Workspace that Revolutionizes Teaching and Learning
            </Translate>
          </p>
          <p className={styles.heroDescription}>
            <Translate id="education.hero.description">
              FunBlocks AI deeply integrates artificial intelligence technology with educational theory, providing powerful thinking tools for educators and learners to cultivate core competencies for the future.
            </Translate>
          </p>
          <div className={styles.heroCTA}>
            <Link
              to="https://www.funblocks.net"
              className={clsx(styles.ctaButton, styles.ctaPrimary)}
            >
              <Translate id="education.hero.cta.primary">Try for Free</Translate>
            </Link>
            <Link
              to="https://www.funblocks.net/aiflow"
              className={clsx(styles.ctaButton, styles.ctaSecondary)}
            >
              <Translate id="education.hero.cta.secondary">Watch Demo</Translate>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}

// Challenges Section Component
function ChallengesSection() {
  const challenges = [
    {
      icon: '📚',
      titleId: 'education.challenges.workload.title',
      descriptionId: 'education.challenges.workload.description',
    },
    {
      icon: '😴',
      titleId: 'education.challenges.engagement.title',
      descriptionId: 'education.challenges.engagement.description',
    },
    {
      icon: '🎯',
      titleId: 'education.challenges.diversity.title',
      descriptionId: 'education.challenges.diversity.description',
    },
    {
      icon: '🚀',
      titleId: 'education.challenges.skills.title',
      descriptionId: 'education.challenges.skills.description',
    },
  ];

  return (
    <section className={styles.section}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.challenges.title">
            Challenges Facing Today's Education
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.challenges.description">
            Educators and learners face unprecedented challenges in the modern educational environment
          </Translate>
        </p>
        <div className={styles.challengesGrid}>
          {challenges.map((challenge, index) => (
            <div key={index} className={styles.challengeCard}>
              <span className={styles.challengeIcon}>{challenge.icon}</span>
              <h3 className={styles.challengeTitle}>
                <Translate id={challenge.titleId} />
              </h3>
              <p className={styles.challengeDescription}>
                <Translate id={challenge.descriptionId} />
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Solution Section Component
function SolutionSection() {
  const solutions = [
    {
      icon: '🔧',
      titleId: 'education.solution.workspace.title',
      descriptionId: 'education.solution.workspace.description',
    },
    {
      icon: '🧠',
      titleId: 'education.solution.thinking.title',
      descriptionId: 'education.solution.thinking.description',
    },
    {
      icon: '👁️',
      titleId: 'education.solution.visual.title',
      descriptionId: 'education.solution.visual.description',
    },
  ];

  return (
    <section className={clsx(styles.section, styles.solutionSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.solution.title">
            FunBlocks AI: Your All-in-One Educational Co-pilot
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.solution.description">
            FunBlocks AI provides comprehensive and powerful solutions to systematically enhance teaching and learning efficiency and depth
          </Translate>
        </p>
        <div className={styles.solutionGrid}>
          {solutions.map((solution, index) => (
            <div key={index} className={styles.solutionCard}>
              <span className={styles.solutionIcon}>{solution.icon}</span>
              <h3 className={styles.solutionTitle}>
                <Translate id={solution.titleId} />
              </h3>
              <p className={styles.solutionDescription}>
                <Translate id={solution.descriptionId} />
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Features Section Component
function FeaturesSection() {
  const features = [
    {
      titleId: 'education.features.visualization.title',
      subtitleId: 'education.features.visualization.subtitle',
      items: [
        'education.features.visualization.mindmap',
        'education.features.visualization.infographic',
      ],
      benefitId: 'education.features.visualization.benefit',
    },
    {
      titleId: 'education.features.content.title',
      subtitleId: 'education.features.content.subtitle',
      items: [
        'education.features.content.slides',
        'education.features.content.docs',
      ],
      benefitId: 'education.features.content.benefit',
    },
    {
      titleId: 'education.features.thinking.title',
      subtitleId: 'education.features.thinking.subtitle',
      items: [
        'education.features.thinking.bloom',
        'education.features.thinking.critical',
        'education.features.thinking.creative',
      ],
      benefitId: 'education.features.thinking.benefit',
    },
  ];

  return (
    <section className={clsx(styles.section, styles.featuresSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.features.title">
            Core Features: Unleashing Educational Potential
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.features.description">
            FunBlocks AI provides a series of powerful tools that deeply integrate into all aspects of education
          </Translate>
        </p>
        <div className={styles.featuresGrid}>
          {features.map((feature, index) => (
            <div key={index} className={styles.featureCard}>
              <div className={styles.featureHeader}>
                <h3 className={styles.featureTitle}>
                  <Translate id={feature.titleId} />
                </h3>
                <p className={styles.featureSubtitle}>
                  <Translate id={feature.subtitleId} />
                </p>
              </div>
              <ul className={styles.featureList}>
                {feature.items.map((itemId, itemIndex) => (
                  <li key={itemIndex}>
                    <Translate id={itemId} />
                  </li>
                ))}
              </ul>
              <div className={styles.featureBenefit}>
                <Translate id={feature.benefitId} />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Benefits Section Component
function BenefitsSection() {
  const educatorBenefits = [
    'education.benefits.educators.workload',
    'education.benefits.educators.differentiation',
    'education.benefits.educators.engagement',
    'education.benefits.educators.growth',
  ];

  const studentBenefits = [
    'education.benefits.students.personalized',
    'education.benefits.students.understanding',
    'education.benefits.students.engagement',
    'education.benefits.students.skills',
  ];

  return (
    <section className={clsx(styles.section, styles.benefitsSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.benefits.title">
            Tangible Benefits for the Learning Community
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.benefits.description">
            FunBlocks AI is committed to bringing real value to the entire learning community
          </Translate>
        </p>
        <div className={styles.benefitsGrid}>
          <div className={styles.benefitCategory}>
            <h3 className={styles.benefitTitle}>
              <Translate id="education.benefits.educators.title">
                Empowering Educators
              </Translate>
            </h3>
            <ul className={styles.benefitList}>
              {educatorBenefits.map((benefitId, index) => (
                <li key={index}>
                  <Translate id={benefitId} />
                </li>
              ))}
            </ul>
          </div>
          <div className={styles.benefitCategory}>
            <h3 className={styles.benefitTitle}>
              <Translate id="education.benefits.students.title">
                Enhancing Student Learning Experience
              </Translate>
            </h3>
            <ul className={styles.benefitList}>
              {studentBenefits.map((benefitId, index) => (
                <li key={index}>
                  <Translate id={benefitId} />
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}

// Case Studies Section Component
function CaseStudiesSection() {
  const cases = [
    {
      titleId: 'education.cases.university.title',
      descriptionId: 'education.cases.university.description',
      results: [
        'education.cases.university.result1',
        'education.cases.university.result2',
        'education.cases.university.result3',
      ],
      icon: '🎓',
    },
    {
      titleId: 'education.cases.medical.title',
      descriptionId: 'education.cases.medical.description',
      results: [
        'education.cases.medical.result1',
        'education.cases.medical.result2',
        'education.cases.medical.result3',
      ],
      icon: '🏥',
    },
    {
      titleId: 'education.cases.k12.title',
      descriptionId: 'education.cases.k12.description',
      results: [
        'education.cases.k12.result1',
        'education.cases.k12.result2',
        'education.cases.k12.result3',
      ],
      icon: '🏫',
    },
  ];

  return (
    <section className={styles.section}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.cases.title">
            Real Educational Success Stories
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.cases.description">
            Learn how educational institutions and individuals are transforming teaching and learning through FunBlocks AI
          </Translate>
        </p>
        <div className={styles.casesGrid}>
          {cases.map((caseStudy, index) => (
            <div key={index} className={styles.caseCard}>
              <div className={styles.caseHeader}>
                <span className={styles.caseIcon}>{caseStudy.icon}</span>
                <h3 className={styles.caseTitle}>
                  <Translate id={caseStudy.titleId} />
                </h3>
              </div>
              <p className={styles.caseDescription}>
                <Translate id={caseStudy.descriptionId} />
              </p>
              <div className={styles.caseResults}>
                {caseStudy.results.map((resultId, resultIndex) => (
                  <div key={resultIndex} className={styles.caseResult}>
                    <span className={styles.resultIcon}>✅</span>
                    <Translate id={resultId} />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Integration Section Component
function IntegrationSection() {
  return (
    <section className={clsx(styles.section, styles.integrationSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.integration.title">
            Seamless Integration: Fitting into Your Educational Environment
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.integration.description">
            FunBlocks AI is designed with focus on usability, accessibility, security, and compatibility with existing ecosystems
          </Translate>
        </p>
        <div className={styles.integrationGrid}>
          <div className={styles.integrationCard}>
            <h3 className={styles.integrationTitle}>
              <span className={styles.integrationIcon}>🚀</span>
              <Translate id="education.integration.accessibility.title">
                User-Friendly and Accessible
              </Translate>
            </h3>
            <ul className={styles.integrationList}>
              <li>
                <Translate id="education.integration.accessibility.free">
                  Free Trial: New users can experience core features for free
                </Translate>
              </li>
              <li>
                <Translate id="education.integration.accessibility.api">
                  Bring Your Own API Key: Use your own AI model API to reduce costs
                </Translate>
              </li>
              <li>
                <Translate id="education.integration.accessibility.subscription">
                  Flexible Subscription: One subscription, all models
                </Translate>
              </li>
            </ul>
          </div>
          <div className={styles.integrationCard}>
            <h3 className={styles.integrationTitle}>
              <span className={styles.integrationIcon}>🔒</span>
              <Translate id="education.integration.security.title">
                Data Privacy and Security
              </Translate>
            </h3>
            <ul className={styles.integrationList}>
              <li>
                <Translate id="education.integration.security.encryption">
                  SSL encryption and HTTPS protocol protect data transmission
                </Translate>
              </li>
              <li>
                <Translate id="education.integration.security.access">
                  Strict access control mechanisms
                </Translate>
              </li>
              <li>
                <Translate id="education.integration.security.compliance">
                  Follow data protection principles and best practices
                </Translate>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}

// Future Section Component
function FutureSection() {
  const futureAspects = [
    {
      titleId: 'education.future.collaboration.title',
      descriptionId: 'education.future.collaboration.description',
      icon: '🤝',
    },
    {
      titleId: 'education.future.adaptation.title',
      descriptionId: 'education.future.adaptation.description',
      icon: '🌟',
    },
    {
      titleId: 'education.future.evolution.title',
      descriptionId: 'education.future.evolution.description',
      icon: '👨‍🏫',
    },
  ];

  return (
    <section className={clsx(styles.section, styles.futureSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.future.title">
            Planning the Future: FunBlocks AI and Educational Evolution
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.future.description">
            FunBlocks AI is committed to shaping the future of human-AI collaborative education
          </Translate>
        </p>
        <div className={styles.futureGrid}>
          {futureAspects.map((aspect, index) => (
            <div key={index} className={styles.futureCard}>
              <span className={styles.futureIcon}>{aspect.icon}</span>
              <h3 className={styles.futureTitle}>
                <Translate id={aspect.titleId} />
              </h3>
              <p className={styles.futureDescription}>
                <Translate id={aspect.descriptionId} />
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

// CTA Section Component
function CTASection() {
  return (
    <section className={clsx(styles.section, styles.ctaSection)}>
      <div className="container">
        <div className={styles.ctaContent}>
          <Heading as="h2" className={styles.ctaTitle}>
            <Translate id="education.cta.title">
              Partner with FunBlocks AI to Revolutionize Your Teaching and Learning
            </Translate>
          </Heading>
          <p className={styles.ctaDescription}>
            <Translate id="education.cta.description">
              Choosing FunBlocks AI is not just choosing an advanced technological tool, but embracing a smarter, more efficient, and more human-centered educational future
            </Translate>
          </p>
          <div className={styles.ctaButtons}>
            <Link
              to="https://www.funblocks.net"
              className={clsx(styles.ctaButton, styles.ctaPrimary)}
            >
              <Translate id="education.cta.primary">Start Free Trial Now</Translate>
            </Link>
            <Link
              to="https://www.funblocks.net/aitools"
              className={clsx(styles.ctaButton, styles.ctaSecondary)}
            >
              <Translate id="education.cta.secondary">Learn More About Product Features</Translate>
            </Link>
          </div>
          <div className={styles.ctaFeatures}>
            <Translate id="education.cta.features">
              ✓ Free trial of all core features
              ✓ No credit card required
              ✓ Start using immediately
            </Translate>
          </div>
        </div>
      </div>
    </section>
  );
}

export default function EducationPage() {
  return (
    <Layout
      title={translate({
        id: 'education.hero.title',
        message: 'FunBlocks AI Empowers Education: Ushering in a New Era of Intelligent Learning',
      })}
      description={translate({
        id: 'education.hero.description',
        message: 'FunBlocks AI deeply integrates artificial intelligence technology with educational theory, providing powerful thinking tools for educators and learners to cultivate core competencies for the future.',
      })}
    >
      <EducationStructuredData />
      <HeroSection />
      <ChallengesSection />
      <SolutionSection />
      <FeaturesSection />
      <BenefitsSection />
      <CaseStudiesSection />
      <IntegrationSection />
      <FutureSection />
      <EducationFAQ />
      <CTASection />
    </Layout>
  );
}
