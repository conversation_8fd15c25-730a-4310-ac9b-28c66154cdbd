/* Education Solutions Page Styles */

.heroSection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 120px 0 80px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.heroSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.heroContent {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.heroSubtitle {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.heroDescription {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  opacity: 0.8;
  line-height: 1.6;
}

.heroCTA {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.ctaButton {
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.ctaPrimary {
  background: white;
  color: #667eea;
}

.ctaPrimary:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.ctaSecondary {
  background: transparent;
  color: white;
  border-color: white;
}

.ctaSecondary:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
}

.section {
  padding: 80px 0;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  color: #2d3748;
}

.sectionDescription {
  font-size: 1.2rem;
  text-align: center;
  margin-bottom: 3rem;
  color: #4a5568;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.challengesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.challengeCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border-left: 4px solid #e53e3e;
  transition: transform 0.3s ease;
}

.challengeCard:hover {
  transform: translateY(-5px);
}

.challengeIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.challengeTitle {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
}

.challengeDescription {
  color: #4a5568;
  line-height: 1.6;
}

.solutionSection {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.solutionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.solutionCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border-top: 4px solid #667eea;
  transition: all 0.3s ease;
}

.solutionCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.solutionIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  color: #667eea;
}

.solutionTitle {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
}

.solutionDescription {
  color: #4a5568;
  line-height: 1.6;
}

.featuresSection {
  background: white;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

.featureCard {
  background: #f7fafc;
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.featureCard:hover {
  background: white;
  box-shadow: 0 10px 40px rgba(0,0,0,0.1);
  transform: translateY(-5px);
}

.featureHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.featureTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.featureSubtitle {
  color: #667eea;
  font-weight: 500;
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.featureList li {
  padding: 0.75rem 0;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.featureList li:last-child {
  border-bottom: none;
}

.featureList li::before {
  content: '✨';
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.featureBenefit {
  background: #667eea;
  color: white;
  padding: 1rem;
  border-radius: 12px;
  margin-top: 1.5rem;
  font-weight: 500;
  text-align: center;
}

.benefitsSection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.benefitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

.benefitCategory {
  background: rgba(255,255,255,0.1);
  border-radius: 20px;
  padding: 2.5rem;
  backdrop-filter: blur(10px);
}

.benefitTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
}

.benefitList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefitList li {
  padding: 1rem 0;
  border-bottom: 1px solid rgba(255,255,255,0.2);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.benefitList li:last-child {
  border-bottom: none;
}

.benefitList li::before {
  content: '🎯';
  flex-shrink: 0;
}

.casesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.caseCard {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border-top: 4px solid #38a169;
  transition: all 0.3s ease;
}

.caseCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.caseHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.caseIcon {
  font-size: 2.5rem;
}

.caseTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.caseDescription {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.caseResults {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.caseResult {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #2d3748;
  font-weight: 500;
}

.resultIcon {
  color: #38a169;
  flex-shrink: 0;
}

.integrationSection {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.integrationGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.integrationCard {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.integrationCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.integrationTitle {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.integrationIcon {
  font-size: 1.5rem;
}

.integrationList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.integrationList li {
  padding: 0.75rem 0;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.integrationList li:last-child {
  border-bottom: none;
}

.integrationList li::before {
  content: '✓';
  color: #38a169;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.futureSection {
  background: white;
}

.futureGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.futureCard {
  background: #f7fafc;
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.futureCard:hover {
  background: white;
  box-shadow: 0 10px 40px rgba(0,0,0,0.1);
  transform: translateY(-5px);
}

.futureIcon {
  font-size: 3rem;
  display: block;
  margin-bottom: 1.5rem;
}

.futureTitle {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.futureDescription {
  color: #4a5568;
  line-height: 1.6;
}

.ctaSection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.ctaContent {
  max-width: 800px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.ctaDescription {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.ctaFeatures {
  font-size: 1rem;
  opacity: 0.8;
  white-space: pre-line;
  line-height: 1.8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }

  .heroSubtitle {
    font-size: 1.2rem;
  }

  .heroDescription {
    font-size: 1rem;
  }

  .heroCTA {
    flex-direction: column;
    align-items: center;
  }

  .ctaButton {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .challengesGrid,
  .solutionGrid,
  .featuresGrid,
  .benefitsGrid,
  .casesGrid,
  .integrationGrid,
  .futureGrid {
    grid-template-columns: 1fr;
  }

  .section {
    padding: 60px 0;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .ctaTitle {
    font-size: 2rem;
  }

  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
}
