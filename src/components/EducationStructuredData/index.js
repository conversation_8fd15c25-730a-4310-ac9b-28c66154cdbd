import React from 'react';
import { translate } from '@docusaurus/Translate';

function EducationStructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": translate({
      id: 'education.hero.title',
      message: 'FunBlocks AI Empowers Education: Ushering in a New Era of Intelligent Learning',
    }),
    "description": translate({
      id: 'education.hero.description',
      message: 'FunBlocks AI deeply integrates artificial intelligence technology with educational theory, providing powerful thinking tools for educators and learners to cultivate core competencies for the future.',
    }),
    "url": "https://docs.funblocks.net/education",
    "mainEntity": {
      "@type": "SoftwareApplication",
      "name": "FunBlocks AI",
      "applicationCategory": "EducationalApplication",
      "operatingSystem": "Web Browser",
      "description": translate({
        id: 'education.solution.description',
        message: 'FunBlocks AI provides comprehensive and powerful solutions to systematically enhance teaching and learning efficiency and depth',
      }),
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock",
        "description": "Free trial available"
      },
      "featureList": [
        "AI Mind Mapping",
        "AI Presentation Generation", 
        "AI Document Creation",
        "Critical Thinking Tools",
        "Educational Framework Integration",
        "Visual Learning Tools"
      ],
      "applicationSubCategory": [
        "Mind Mapping Software",
        "Presentation Software",
        "Educational Technology",
        "Artificial Intelligence Tools"
      ]
    },
    "about": [
      {
        "@type": "Thing",
        "name": "Educational Technology",
        "description": "Technology tools designed to enhance teaching and learning"
      },
      {
        "@type": "Thing", 
        "name": "Artificial Intelligence in Education",
        "description": "AI applications that support educational processes"
      },
      {
        "@type": "Thing",
        "name": "Mind Mapping",
        "description": "Visual thinking and knowledge organization technique"
      },
      {
        "@type": "Thing",
        "name": "Critical Thinking",
        "description": "Analytical thinking skills development"
      },
      {
        "@type": "Thing",
        "name": "Creative Thinking", 
        "description": "Innovation and creative problem-solving skills"
      }
    ],
    "audience": [
      {
        "@type": "EducationalAudience",
        "educationalRole": "teacher"
      },
      {
        "@type": "EducationalAudience", 
        "educationalRole": "student"
      },
      {
        "@type": "EducationalAudience",
        "educationalRole": "administrator"
      }
    ],
    "provider": {
      "@type": "Organization",
      "name": "FunBlocks",
      "url": "https://www.funblocks.net"
    }
  };

  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "What is FunBlocks AI?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "FunBlocks AI is an all-in-one AI workspace that integrates artificial intelligence technology with educational theory to provide powerful thinking tools for educators and learners."
        }
      },
      {
        "@type": "Question",
        "name": "How does FunBlocks AI help educators?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "FunBlocks AI helps educators by reducing administrative and lesson preparation burden, promoting differentiated instruction, creating engaging interactive materials, and supporting professional growth."
        }
      },
      {
        "@type": "Question",
        "name": "What educational frameworks does FunBlocks AI support?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "FunBlocks AI supports various educational frameworks including Bloom's Taxonomy, Marzano's Taxonomy, SOLO Taxonomy, and DOK (Depth of Knowledge) framework through specialized AI tools."
        }
      },
      {
        "@type": "Question",
        "name": "Is FunBlocks AI suitable for all education levels?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, FunBlocks AI is designed to support education across all levels, from K-12 to higher education, with tools that can be adapted to different learning needs and complexity levels."
        }
      },
      {
        "@type": "Question",
        "name": "How does FunBlocks AI ensure data privacy and security?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "FunBlocks AI uses SSL encryption, HTTPS protocols, strict access control mechanisms, and follows data protection principles and best practices to ensure user data privacy and security."
        }
      }
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />
    </>
  );
}

export default EducationStructuredData;
