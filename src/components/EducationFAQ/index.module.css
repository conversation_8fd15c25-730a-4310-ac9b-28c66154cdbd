.faqSection {
  padding: 80px 0;
  background: #f7fafc;
}

.faqTitle {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  color: #2d3748;
}

.faqDescription {
  font-size: 1.2rem;
  text-align: center;
  margin-bottom: 3rem;
  color: #4a5568;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.faqContainer {
  max-width: 800px;
  margin: 0 auto;
}

.faqItem {
  background: white;
  border-radius: 12px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.faqItem:hover {
  box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.faqItem.open {
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
}

.faqQuestion {
  width: 100%;
  padding: 1.5rem 2rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.faqQuestion:hover {
  background: #f7fafc;
}

.faqItem.open .faqQuestion {
  background: #667eea;
  color: white;
}

.faqIcon {
  font-size: 1.5rem;
  font-weight: 300;
  transition: transform 0.3s ease;
  flex-shrink: 0;
  margin-left: 1rem;
}

.faqItem.open .faqIcon {
  transform: rotate(180deg);
}

.faqAnswer {
  padding: 0 2rem 1.5rem 2rem;
  color: #4a5568;
  line-height: 1.6;
  animation: slideDown 0.3s ease;
}

.faqAnswer p {
  margin: 0;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .faqSection {
    padding: 60px 0;
  }
  
  .faqTitle {
    font-size: 2rem;
  }
  
  .faqDescription {
    font-size: 1rem;
  }
  
  .faqQuestion {
    padding: 1.25rem 1.5rem;
    font-size: 1rem;
  }
  
  .faqAnswer {
    padding: 0 1.5rem 1.25rem 1.5rem;
  }
  
  .faqIcon {
    font-size: 1.25rem;
  }
}
