import React, { useState } from 'react';
import Translate from '@docusaurus/Translate';
import Heading from '@theme/Heading';
import clsx from 'clsx';
import styles from './index.module.css';

function FAQItem({ question, answer, isOpen, onToggle }) {
  return (
    <div className={clsx(styles.faqItem, { [styles.open]: isOpen })}>
      <button className={styles.faqQuestion} onClick={onToggle}>
        <span>{question}</span>
        <span className={styles.faqIcon}>{isOpen ? '−' : '+'}</span>
      </button>
      {isOpen && (
        <div className={styles.faqAnswer}>
          <p>{answer}</p>
        </div>
      )}
    </div>
  );
}

function EducationFAQ() {
  const [openItems, setOpenItems] = useState(new Set([0])); // First item open by default

  const faqData = [
    {
      question: (
        <Translate id="education.faq.q1.question">
          What is FunBlocks AI and how does it help in education?
        </Translate>
      ),
      answer: (
        <Translate id="education.faq.q1.answer">
          FunBlocks AI is an all-in-one AI workspace that integrates artificial intelligence technology with educational theory. It provides powerful thinking tools including AI mind mapping, presentation generation, critical thinking frameworks, and visual learning tools to help educators create engaging materials and students develop 21st-century skills.
        </Translate>
      ),
    },
    {
      question: (
        <Translate id="education.faq.q2.question">
          How does FunBlocks AI reduce teacher workload?
        </Translate>
      ),
      answer: (
        <Translate id="education.faq.q2.answer">
          FunBlocks AI automates and simplifies many time-consuming tasks such as creating presentations, generating mind maps, designing infographics, and structuring lesson plans based on educational frameworks like Bloom's Taxonomy. This allows teachers to focus more on direct student interaction and creative teaching approaches.
        </Translate>
      ),
    },
    {
      question: (
        <Translate id="education.faq.q3.question">
          What educational frameworks does FunBlocks AI support?
        </Translate>
      ),
      answer: (
        <Translate id="education.faq.q3.answer">
          FunBlocks AI supports various proven educational frameworks including Bloom's Taxonomy (BloomBrain), Marzano's Taxonomy (MarzanoBrain), SOLO Taxonomy (SOLOBrain), and Depth of Knowledge (DOKBrain). These tools help structure learning experiences according to established pedagogical principles.
        </Translate>
      ),
    },
    {
      question: (
        <Translate id="education.faq.q4.question">
          Is FunBlocks AI suitable for all education levels?
        </Translate>
      ),
      answer: (
        <Translate id="education.faq.q4.answer">
          Yes, FunBlocks AI is designed to support education across all levels, from K-12 to higher education and professional development. The tools can be adapted to different complexity levels and learning needs, making them versatile for various educational contexts.
        </Translate>
      ),
    },
    {
      question: (
        <Translate id="education.faq.q5.question">
          How does FunBlocks AI ensure data privacy and security?
        </Translate>
      ),
      answer: (
        <Translate id="education.faq.q5.answer">
          FunBlocks AI prioritizes data security through SSL encryption, HTTPS protocols, strict access control mechanisms, and adherence to data protection principles. The platform also offers a "Bring Your Own API Key" option, giving users control over their data and AI model usage.
        </Translate>
      ),
    },
    {
      question: (
        <Translate id="education.faq.q6.question">
          Can I try FunBlocks AI for free?
        </Translate>
      ),
      answer: (
        <Translate id="education.faq.q6.answer">
          Yes, FunBlocks AI offers a free trial that allows new users to experience all core features without requiring a credit card. Additionally, users can bring their own API keys to access premium features at reduced costs, making it accessible for educational institutions with budget constraints.
        </Translate>
      ),
    },
  ];

  const toggleItem = (index) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index);
    } else {
      newOpenItems.add(index);
    }
    setOpenItems(newOpenItems);
  };

  return (
    <section className={styles.faqSection}>
      <div className="container">
        <Heading as="h2" className={styles.faqTitle}>
          <Translate id="education.faq.title">
            Frequently Asked Questions
          </Translate>
        </Heading>
        <p className={styles.faqDescription}>
          <Translate id="education.faq.description">
            Get answers to common questions about FunBlocks AI in education
          </Translate>
        </p>
        <div className={styles.faqContainer}>
          {faqData.map((item, index) => (
            <FAQItem
              key={index}
              question={item.question}
              answer={item.answer}
              isOpen={openItems.has(index)}
              onToggle={() => toggleItem(index)}
            />
          ))}
        </div>
      </div>
    </section>
  );
}

export default EducationFAQ;
